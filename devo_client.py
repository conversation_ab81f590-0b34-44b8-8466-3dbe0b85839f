"""
Devo API client for data backup system.
Handles authentication, rate limiting, retries, and robust error handling.
"""

import time
import json
import hashlib
import hmac
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from config import config
from logger import logger
from date_utils import DateUtils


class DevoAPIError(Exception):
    """Custom exception for Devo API errors."""
    pass


class DevoClient:
    """Robust Devo API client with comprehensive error handling."""
    
    def __init__(self):
        """Initialize the Devo API client."""
        self.api_key = config.devo_api_key
        self.api_secret = config.devo_api_secret
        self.query_endpoint = config.devo_query_endpoint
        self.timeout = config.api_timeout
        self.max_retries = config.max_retries
        self.retry_delay = config.retry_delay
        self.rate_limit_delay = config.rate_limit_delay
        
        # Setup session with retry strategy
        self.session = self._create_session()
        
        logger.info(f"Devo client initialized with endpoint: {self.query_endpoint}")
    
    def _create_session(self) -> requests.Session:
        """Create a requests session with retry strategy."""
        session = requests.Session()
        
        # Define retry strategy
        retry_strategy = Retry(
            total=self.max_retries,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "POST"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def _generate_signature(self, method: str, path: str, timestamp: str, body: str = "") -> str:
        """
        Generate HMAC signature for Devo API authentication.
        
        Args:
            method: HTTP method
            path: API path
            timestamp: Unix timestamp
            body: Request body
            
        Returns:
            HMAC signature
        """
        # Create the string to sign
        string_to_sign = f"{method}\n{path}\n{timestamp}\n{body}"
        
        # Generate HMAC signature
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    def _get_auth_headers(self, method: str, path: str, body: str = "") -> Dict[str, str]:
        """
        Generate authentication headers for Devo API.
        
        Args:
            method: HTTP method
            path: API path
            body: Request body
            
        Returns:
            Dictionary of authentication headers
        """
        timestamp = str(int(time.time()))
        signature = self._generate_signature(method, path, timestamp, body)
        
        return {
            'Authorization': f'Bearer {self.api_key}',
            'X-Devo-Signature': signature,
            'X-Devo-Timestamp': timestamp,
            'Content-Type': 'application/json'
        }
    
    def _make_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """
        Make HTTP request with error handling and rate limiting.
        
        Args:
            method: HTTP method
            url: Request URL
            **kwargs: Additional request parameters
            
        Returns:
            Response object
            
        Raises:
            DevoAPIError: If request fails after retries
        """
        for attempt in range(self.max_retries + 1):
            try:
                logger.log_api_request(method, url)
                
                response = self.session.request(
                    method=method,
                    url=url,
                    timeout=self.timeout,
                    **kwargs
                )
                
                logger.log_api_request(method, url, response.status_code)
                
                # Handle rate limiting
                if response.status_code == 429:
                    retry_after = int(response.headers.get('Retry-After', self.rate_limit_delay))
                    logger.log_rate_limit(retry_after)
                    time.sleep(retry_after)
                    continue
                
                # Check for successful response
                if response.status_code == 200:
                    return response
                
                # Handle other HTTP errors
                error_msg = f"HTTP {response.status_code}: {response.text}"
                if attempt < self.max_retries:
                    logger.log_retry(attempt + 1, self.max_retries, error_msg)
                    time.sleep(self.retry_delay * (attempt + 1))  # Exponential backoff
                    continue
                else:
                    raise DevoAPIError(error_msg)
                    
            except requests.exceptions.RequestException as e:
                error_msg = f"Request exception: {str(e)}"
                if attempt < self.max_retries:
                    logger.log_retry(attempt + 1, self.max_retries, error_msg)
                    time.sleep(self.retry_delay * (attempt + 1))
                    continue
                else:
                    raise DevoAPIError(error_msg)
        
        raise DevoAPIError("Max retries exceeded")
    
    def query_data(self, query: str, start_time: str, end_time: str) -> Dict[str, Any]:
        """
        Execute a query against Devo API.
        
        Args:
            query: Devo query string
            start_time: Start time in ISO format
            end_time: End time in ISO format
            
        Returns:
            Query results as dictionary
            
        Raises:
            DevoAPIError: If query fails
        """
        # Prepare query payload
        payload = {
            "query": query,
            "from": start_time,
            "to": end_time,
            "mode": {
                "type": "json"
            }
        }
        
        body = json.dumps(payload)
        path = "/search/query"
        
        # Generate authentication headers
        headers = self._get_auth_headers("POST", path, body)
        
        try:
            response = self._make_request(
                method="POST",
                url=self.query_endpoint,
                headers=headers,
                data=body
            )
            
            return response.json()
            
        except json.JSONDecodeError as e:
            raise DevoAPIError(f"Invalid JSON response: {str(e)}")
        except Exception as e:
            raise DevoAPIError(f"Query failed: {str(e)}")
    
    def get_available_tables(self) -> List[str]:
        """
        Get list of available tables from Devo.
        This is a placeholder - implement based on your Devo setup.
        
        Returns:
            List of table names
        """
        # This would typically be a separate API call to get table metadata
        # For now, return common Devo table patterns
        return [
            "firewall.all",
            "siem.logtrust.web.activity",
            "siem.logtrust.alert.info",
            "my.app.data"  # Replace with your actual tables
        ]
    
    def pull_data_for_date(self, date_str: str, tables: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Pull all data for a specific date.
        
        Args:
            date_str: Date in YYYY-MM-DD format
            tables: List of tables to query (if None, uses default tables)
            
        Returns:
            Dictionary with results for each table
        """
        if tables is None:
            tables = self.get_available_tables()
        
        start_time, end_time = DateUtils.format_date_for_query(date_str)
        results = {}
        
        logger.info(f"Pulling data for date: {date_str} ({len(tables)} tables)")
        
        for table in tables:
            try:
                # Basic query to get all data from table for the date
                query = f"from {table}"
                
                logger.debug(f"Querying table: {table}")
                table_data = self.query_data(query, start_time, end_time)
                
                # Count records
                record_count = len(table_data.get('object', []))
                results[table] = {
                    'data': table_data,
                    'record_count': record_count,
                    'success': True
                }
                
                logger.log_data_pull(f"{date_str}/{table}", record_count, True)
                
                # Rate limiting between table queries
                time.sleep(self.rate_limit_delay)
                
            except Exception as e:
                logger.error(f"Failed to pull data from table {table}: {str(e)}")
                results[table] = {
                    'data': None,
                    'record_count': 0,
                    'success': False,
                    'error': str(e)
                }
                logger.log_data_pull(f"{date_str}/{table}", 0, False)
        
        return results
    
    def test_connection(self) -> bool:
        """
        Test connection to Devo API.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            # Simple test query
            current_date = DateUtils.get_current_date()
            start_time, end_time = DateUtils.format_date_for_query(current_date)
            
            # Use a simple query that should work on most Devo instances
            test_query = "from siem.logtrust.web.activity select count() as test_count"
            
            result = self.query_data(test_query, start_time, end_time)
            logger.info("Devo API connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"Devo API connection test failed: {str(e)}")
            return False
